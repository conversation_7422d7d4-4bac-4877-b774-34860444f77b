import { ScrollView, Text as T, View as V, Image as Img } from '@tarojs/components'
import { useEffect, useRef } from 'react'
import * as cn from 'classnames'
import IconFont from '@/components/IconFont'
import Tag from '../Tag'
import styles from './index.module.scss'
import { VideoImageType } from '../../formatEnterpriseMainView'
import useReducer from '@/hooks/useReducer'
import useTimeout from '@/hooks/useTimeout'
import ImagePreview from '@/components/imagesPreview'

type IProps = {
  data: VideoImageType
  infoId: number
}

type InitState = {
  tagChoose: 'video' | 'image'
  scrollIntoView: 'firstVideo' | 'firstImage' | ''
  showPreview: boolean
  currentIndex: number
}

/**
 * 公司相册
*/
export default (props: IProps) => {
  const { data, infoId } = props
  // 视频渲染的数据
  const renderVideoList = data.videoList.slice(0, 2)
  // 图片渲染的数据
  const renderImageList = data.imageList.slice(0, (12 - renderVideoList.length))
  // 是否有视频
  const hasVideo = !!renderVideoList.length
  // 查看全部的判断
  const showAll = (renderVideoList.length + renderImageList.length) > 4

  const { timeout } = useTimeout()

  // 是否是点击公司视频/公司图片，是点击，onScroll 里面的逻辑不生效
  const isClick = useRef<boolean>(false)

  // 记录scrollView 的位置，点击标签的时候，需要用此字段判断
  const scrollLeftValue = useRef<number>(0)

  const [{
    tagChoose,
    scrollIntoView,
    showPreview,
    currentIndex,
  }, dispatch] = useReducer<InitState>({
    tagChoose: hasVideo ? 'video' : 'image',
    scrollIntoView: hasVideo ? 'firstVideo' : 'firstImage',
    showPreview: false,
    currentIndex: 0,
  })

  // 图片的位置
  const firstImageLeft = useRef<number>(0)

  const changeTag = (t: InitState['tagChoose']) => {
    isClick.current = true
    timeout(() => {
      isClick.current = false
    }, 600)
    // 当前点击，需要选中的
    const currentId = t === 'image' ? 'firstImage' : 'firstVideo'
    if (currentId !== scrollIntoView) {
      dispatch({ tagChoose: t, scrollIntoView: currentId })
      return
    }
    // 点击需要选中的dom id 和 当前已经选中的ID相同。那么需要做特殊处理
    dispatch({ tagChoose: t, scrollIntoView: '' })
    if (t === 'image' && scrollLeftValue.current < firstImageLeft.current) {
      timeout(() => {
        dispatch({ tagChoose: t, scrollIntoView: 'firstImage' })
      }, 100)
    } else {
      timeout(() => {
        dispatch({ tagChoose: t, scrollIntoView: 'firstVideo' })
      }, 100)
    }
  }

  /**
   * 滚动回调，滚动到照片位置时候，tag 选中公司图片
  */
  const onScroll = (e) => {
    // 必须图片和视频都有，才执行这里面的逻辑
    if (!renderImageList.length || !hasVideo) {
      return
    }
    // 点击标签 的 时候，也不执行
    if (isClick.current) return

    const { scrollLeft } = e.detail
    scrollLeftValue.current = scrollLeft
    if (scrollLeft > firstImageLeft.current) {
      tagChoose !== 'image' && dispatch({ tagChoose: 'image' })
    } else {
      tagChoose !== 'video' && dispatch({ tagChoose: 'video' })
    }
  }

  /** 点击查看全部跳转页面 */
  const clickAll = () => {
    $.router.push('/subpackage/company/album/index', {
      infoId,
    })
  }

  // 判断是视频还是图片
  const isVideo = (item: VideoImageType['imageList'][0] | VideoImageType['videoList'][0]) : item is VideoImageType['videoList'][0] => {
    return !!(item as VideoImageType['videoList'][0])?.cover
  }

  /** 点击单个item，实现预览 */
  const clickItem = (item: VideoImageType['imageList'][0] | VideoImageType['videoList'][0], index: number) => {
    // 视频跳转页面预览
    if (isVideo(item)) {
      $.router.push('/subpackage/company/video-preview/index', { url: item?.url })
    } else if ($.isIos()) {
      dispatch({ showPreview: true, currentIndex: index })
    } else {
      const urls = renderImageList.map(it => it.url)
      // 图片使用api 预览
      $.yp.previewImage({ urls, current: index })
    }
  }

  /**
   * 获取节点的宽度
  */
  const getDomWith = () => {
    const SelectorQuery = $.taro.createSelectorQuery()
    SelectorQuery.select('#firstImage')
      .boundingClientRect()
      .exec(([res]) => {
        if (!res) {
          return
        }
        firstImageLeft.current = res.left - 40
      })
  }

  useEffect(() => {
    hasVideo && getDomWith()
  }, [])

  return (
    <V className={styles.wrap}>
      <V className={styles.titleBox}>
        <T className={styles.title}>公司相册</T>
        <V className={styles.more} onClick={clickAll}>
          查看全部
          <IconFont type='yp-mianbaoxue' size={32} color='#FFFFFFA6' />
        </V>
      </V>
      <V className={styles.tagList}>
        { hasVideo && <Tag className={styles.tag} choose={tagChoose === 'video'} isClick={tagChoose !== 'video'} onChange={() => changeTag('video')}>公司视频</Tag> }
        { !!renderImageList.length && <Tag choose={tagChoose === 'image'} isClick={tagChoose !== 'image'} onChange={() => changeTag('image')}>公司图片</Tag> }
      </V>
      <ScrollView scrollX className={styles.scrollView} scrollIntoView={scrollIntoView} onScroll={onScroll} scrollWithAnimation scrollAnimationDuration='300'>
        <V style={{ display: 'flex' }}>
          {
            renderVideoList.map((item, index) => (
              <V className={cn(styles.sourceBox, index === 0 ? styles.first : '')} key={item?.url} id={index === 0 ? 'firstVideo' : ''} onClick={() => clickItem(item, index)}>
                <Img src={item?.cover || ''} className={styles.source} mode="aspectFill"></Img>
                <Img className={styles.videoPlay} src="https://staticscdn.zgzpsjz.com/miniprogram/images/ygd/yp-mini_icon-video-btn.png" mode="aspectFill" />
              </V>
            ))
          }
          {
            renderImageList.map((item, index) => (
              <>
                <V className={cn(styles.sourceBox, index === 0 ? styles.first : '')} key={item?.url} id={index === 0 ? 'firstImage' : ''} onClick={() => clickItem(item, index)}>
                  <Img src={item?.url || ''} className={styles.source} mode="aspectFill"></Img>
                </V>
                {
                  index === renderImageList.length - 1 && !showAll ? (
                    <V style={{ width: '32rpx', height: '1px', opacity: 0 }}>1</V>
                  ) : <V></V>
                }
              </>
            ))
          }
          {/* 查看全部， */}
          {
            showAll && (
              <V>
                <V className={styles.showAllBox} onClick={clickAll}>
                  <T className={styles.showAllText}>查看</T>
                  <V className={styles.showAllText}>全部</V>
                  <Img className={styles.allIcon} src="https://cdn.yupaowang.com/yp_mini/images/zb/yp-mini_fade_right_arrow_icon.png" mode="aspectFill" />
                </V>
                {
                  // 解决ios兼容性
                  <V style={{ width: '32rpx', height: '2px', opacity: 0 }}>1</V>
                }
              </V>
            )
          }
        </V>
      </ScrollView>

      {/* 图片预览 */}
      {showPreview && (
        <ImagePreview
          images={renderImageList?.map(it => it.url)}
          currentIndex={currentIndex}
          onClose={() => {
            dispatch({ showPreview: false, currentIndex: 0 })
          }}
        />
      )}

    </V>
  )
}
