import { Text as T, View as V } from '@tarojs/components'
import IconFont from '@/components/IconFont'
import styles from './index.module.scss'

type IProps = {
  /** 地址 */
  address: string
  /** 经纬度 */
  location: string
}
/**
 * 公司地址
*/
export default (props: IProps) => {
  const { address, location } = props

  /**
   * 打开地图
  */
  const openLocation = () => {
    const [longitude, latitude] = location.split(',')
    // 快手小程序使用原生 ks.openLocation API
    if (process.env.TARO_ENV === 'kwai') {
      ks.openLocation({
        longitude: +longitude,
        latitude: +latitude,
        name: address,
        address,
        scale: 19,
        success: res => {
          console.log(res)
        },
        fail: res => {
          console.log(res)
        },
      })
    } else {
      // 其他平台使用 taro 的 openLocation API
      $.taro.openLocation({
        longitude: +longitude,
        latitude: +latitude,
        name: address,
        address,
        scale: 19,
        success: res => {
          console.log(res)
        },
        fail: res => {
          console.log(res)
        },
      })
    }
  }

  return (
    <V className={styles.wrap}>
      <T className={styles.title}>公司地址</T>
      <V className={styles.inner}>
        <T className={styles.leftInner}> {address} </T>
        {
          location && (
            <V className={styles.nav} onClick={() => openLocation()}>
              <IconFont type='yp-daohang' size={24} color='#fff' />
              <V className={styles.navText}>导航</V>
            </V>
          )
        }
      </V>
    </V>
  )
}
