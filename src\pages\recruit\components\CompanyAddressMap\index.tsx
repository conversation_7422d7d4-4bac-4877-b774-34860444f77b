import { Map, View, Image, Block, Text } from '@tarojs/components'
import { useMemo } from 'react'
import cn from 'classnames'
import { Card } from '../Card'
import s from './index.module.scss'
import IconFont from '@/components/IconFont'
import { useSelector } from '@/core/store'
import useClick from '@/hooks/useClick'
import { haversineDistance } from '@/utils/location'

export default (props: any) => {
  const { info = {}, companyInfo = {} } = props

  const { longitude, latitude } = info.location || {}

  const { name: companyName, logo: companyLogo, companyScaleText, enterpriseBaseInfoId } = companyInfo

  const { latitude: myLat, longitude: myLon } = useSelector((state) => state.storage.userLocation)

  const jumpToEnterprise = useClick(async () => {
    $.router.push('/subpackage/company/index', { infoId: enterpriseBaseInfoId })
  })

  /** 计算距离 */
  const distance = useMemo(() => {
    if (!longitude || !latitude || !myLon || !myLat) {
      return ''
    }
    return haversineDistance(latitude, longitude, myLat, myLon)
  }, [longitude, latitude, myLon, myLat])

  /** 填充地图自定义坐标点 */
  const markers = useMemo(() => {
    if (!latitude || !longitude) return []
    return [{
      longitude: Number(longitude),
      latitude: Number(latitude),
      width: 32,
      height: 32,
      zIndex: 800,
      anchor: { x: 0.5, y: 1 },
      id: 1,
      iconPath: 'https://staticscdn.zgzpsjz.com/miniprogram/images/yk/yp-mini_map_location_icon.png',
    }]
  }, [longitude, latitude])

  const mapComponent = useMemo(() => {
    if (latitude && longitude) {
      return <Block>
        <Map
          longitude={Number(longitude)}
          latitude={Number(latitude)}
          scale={17}
          onError={console.error}
          className={s.map}
          markers={markers}
          onClick={() => {
            // 快手小程序使用原生 ks.openLocation API
            if (process.env.TARO_ENV === 'kwai') {
              ks.openLocation({
                longitude: Number(longitude),
                latitude: Number(latitude),
                scale: 17,
                name: companyName || info.companyName || info.address,
                address: info.addressTitle,
                success: res => {
                  // noop
                  // console.log(res)
                },
                fail: res => {
                  console.error('failed to openLocation', res, latitude, longitude, companyName, info.companyName, info.address)
                },
              })
            } else {
              // 其他平台使用 taro 的 openLocation API
              $.taro.openLocation({
                // @ts-ignore ts 类型错误，此处必须是 Int
                longitude: Number(longitude),
                // @ts-ignore ts 类型错误，此处必须是 Int
                latitude: Number(latitude),
                scale: 17,
                name: companyName || info.companyName || info.address,
                address: info.addressTitle,
                success: res => {
                  // noop
                  // console.log(res)
                },
                fail: res => {
                  console.error('failed to openLocation', res, latitude, longitude, companyName, info.companyName, info.address)
                },
              })
            }
          }}
        ></Map>
        <View className={s.addressContainer}>
          <View className={s.address}>{info.addressTitle}</View>
          <View className={cn(s.distance, { [s.hidden]: !distance })}>{distance}</View>
        </View>
      </Block>
    }
    return <View className={s.emptyMapContainer}>
      {/* <IconFont className={s.ic} type="yp-a-Repositioning_zhongxindingwei" size={28} color='rgb(134, 135, 137)'></IconFont> */}
      <View className={s.ic}><IconFont type="yp-a-Repositioning_zhongxindingwei" size={28} color='rgb(134, 135, 137)'></IconFont></View>
      <Text className={s.addressText}>{info.addressTitle || ''}</Text>
    </View>
  }, [latitude, longitude, info, companyName, distance])

  return <Card>
    <View className={cn(s.companyContainer, { [s.hidden]: !companyName || info.checkDegreeStatus != 2 })} onClick={jumpToEnterprise}>
      <Image src={companyLogo} className={s.companyLogo} mode="aspectFit"></Image>
      <View className={s.companyText}>
        <View className={s.companyName}>{companyName}</View>
        <View className={cn(s.companyDesc, { [s.hidden]: !companyScaleText })}>{companyScaleText}</View>
      </View>
      <IconFont type="yp-mbxl" size={32} color="rgba(0,0,0,.45)"></IconFont>
    </View>
    {mapComponent}
  </Card>
}
